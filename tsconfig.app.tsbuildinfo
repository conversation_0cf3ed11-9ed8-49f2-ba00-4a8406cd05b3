{"root": ["./src/main.ts", "./src/vite-env.d.ts", "./src/api/client.ts", "./src/api/endpoints.ts", "./src/api/types.ts", "./src/components/charts/index.ts", "./src/components/charts/types/index.ts", "./src/config/app.ts", "./src/constants/charttypes.ts", "./src/constants/index.ts", "./src/constants/routes.ts", "./src/pages/dashboard/composables/usedashboardstate.ts", "./src/router/guards.ts", "./src/router/index.ts", "./src/router/utils.ts", "./src/router/modules/dashboard.ts", "./src/router/modules/data.ts", "./src/router/modules/design.ts", "./src/router/modules/error.ts", "./src/router/modules/index.ts", "./src/router/modules/settings.ts", "./src/services/chartservice.ts", "./src/services/dashboardservice.ts", "./src/services/datasourceservice.ts", "./src/services/exportservice.ts", "./src/services/index.ts", "./src/stores/index.ts", "./src/stores/modules/app.ts", "./src/stores/modules/dashboard.ts", "./src/stores/modules/datasource.ts", "./src/types/chart.ts", "./src/types/dashboard.ts", "./src/types/datasource.ts", "./src/types/index.ts", "./src/utils/date.ts", "./src/utils/format.ts", "./src/utils/index.ts", "./src/utils/storage.ts", "./src/utils/validators.ts", "./src/utils/chart/calculations.ts", "./src/utils/chart/formatters.ts", "./src/utils/data/parsers.ts", "./src/app.vue", "./src/components/charts/chartpreview.vue", "./src/components/charts/chartpreview1.vue", "./src/components/charts/kpicard.vue", "./src/components/charts/types/barchart.vue", "./src/components/charts/types/cardchart.vue", "./src/components/charts/types/linechart.vue", "./src/components/charts/types/piechart.vue", "./src/components/charts/types/scatterchart.vue", "./src/components/common/notificationbell.vue", "./src/components/common/notificationcontainer.vue", "./src/components/common/searchinput.vue", "./src/components/common/usermenu.vue", "./src/components/dashboard/dashboardchart.vue", "./src/components/demo/helloworld.vue", "./src/components/layout/appfooter.vue", "./src/components/layout/appheader.vue", "./src/components/layout/mobilenavlink.vue", "./src/components/layout/navlink.vue", "./src/components/ui/confirmdialog.vue", "./src/components/ui/toast.vue", "./src/layouts/authlayout.vue", "./src/layouts/defaultlayout.vue", "./src/layouts/errorlayout.vue", "./src/layouts/layoutwrapper.vue", "./src/layouts/mainlayout.vue", "./src/pages/auth/login.vue", "./src/pages/dashboard/dashboardstore.vue", "./src/pages/dashboard/home.vue", "./src/pages/dashboard/quickdashboard.vue", "./src/pages/dashboard/components/chartpanel.vue", "./src/pages/dashboard/components/datapanel.vue", "./src/pages/data/datasources.vue", "./src/pages/design/templatedesigner.vue", "./src/pages/error/error.vue", "./src/pages/error/notfound.vue", "./src/pages/error/unauthorized.vue", "./src/pages/settings/settings.vue"], "errors": true, "version": "5.8.3"}