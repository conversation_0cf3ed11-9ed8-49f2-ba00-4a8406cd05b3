<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center h-16 justify-between">
          <div class="flex items-center gap-2">
            <button
              @click="goBack"
              class="mr-4 text-gray-400 hover:text-gray-600"
            >
              <ArrowLeftIcon class="h-6 w-6" />
            </button>
            <input
              id="dashboardName"
              v-model="dashboardName"
              type="text"
              placeholder="Enter dashboard name"
              class="text-xl font-semibold text-gray-900 bg-transparent border-none focus:ring-0 focus:border-b-2 focus:border-primary-500 px-1 py-0.5 w-64"
            />
          </div>
          <div class="flex items-center gap-3 ml-auto">
            <button
              class="inline-flex items-center px-4 py-2 border border-primary-200 text-sm font-medium rounded-md text-primary-700 bg-white hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
              title="Share dashboard"
            >
              <ShareIcon class="h-4 w-4 mr-2" />
              Share
            </button>
            <button
              @click="previewMode = true"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-primary-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A2 2 0 0020 6.382V5a2 2 0 00-2-2H6a2 2 0 00-2 2v1.382a2 2 0 00.447 1.342L9 10m6 0v4m0 0l-4.553 2.276A2 2 0 014 17.618V19a2 2 0 002 2h12a2 2 0 002-2v-1.382a2 2 0 00-.447-1.342L15 14z" /></svg>
              Preview
            </button>
            <button
              @click="saveDashboard"
              :disabled="!dashboardName"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <DocumentCheckIcon class="h-4 w-4 mr-2" />
              Save Dashboard
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="flex h-[calc(100vh-4rem)]">
      <!-- Left Sidebar with Tabs -->
      <DataPanel
        v-if="!previewMode"
        ref="dataPanelRef"
        :selectedDataSources="selectedDataSources"
        :expandedDataSources="expandedDataSources"
        :isFieldInUse="isFieldInUse"
        :width="leftSidebarWidth"
        :category="dashboardCategory"
        :description="dashboardDescription"
        @open-manager="openDataSourceManager"
        @toggle-expand="toggleDataSource"
        @field-drag="onFieldDragStart"
        @update-selected-data-sources="updateSelectedDataSources"
        @toggle-dashboard-tabs="handleToggleDashboardTabs"
        @update-dashboard-info="onUpdateDashboardInfo"
      />

      <!-- Draggable Divider (between left sidebar and chart type col) -->
      <div
        v-if="!previewMode"
        class="resizer"
        @mousedown="startResizing('left')"
        :style="{ cursor: 'col-resize', width: '6px', background: '#e5e7eb', zIndex: 20 }"
      ></div>

      <!-- Chart Type & Properties Column -->
      <ChartPanel
        v-if="!previewMode"
        :chartTypes="chartTypes"
        :selectedChartType="selectedChartType"
        :chartConfig="chartConfig"
        :colorSchemes="colorSchemes"
        :colorPalettes="colorPalettes"
        :isChartConfigValid="isChartConfigValid"
        :editingChartId="editingChartId"
        :selectedDataSources="selectedDataSources"
        :width="chartTypeColWidth"
        :alwaysShowProperties="true"
        @update:selectedChartType="selectedChartType = $event"
        @field-drop="onFieldDrop"
        @remove-x-axis="(idx) => { if (Array.isArray(chartConfig.xAxis)) chartConfig.xAxis.splice(idx, 1) }"
        @add-or-update-chart="addOrUpdateChart"
        @cancel-edit="cancelEdit"
      />

      <!-- Draggable Divider (between chart type col and main dashboard) -->
      <div
        v-if="!previewMode"
        class="resizer"
        @mousedown="startResizing('chartType')"
        :style="{ cursor: 'col-resize', width: '6px', background: '#e5e7eb', zIndex: 20 }"
      ></div>

      <!-- Main Dashboard Area -->
      <div 
        :class="['flex-1 p-3']" 
        style="position:relative;"
        @drop="onChartTypeDrop"
        @dragover="onDragOver"
        @dragenter.prevent="onDragEnter"
        @dragleave="onDragLeave"
      >
        <!-- Drag Preview Shadow -->
        <div 
          v-if="showDragPreview"
          class="absolute border-2 border-dashed border-primary-400 bg-primary-50 bg-opacity-30 rounded-lg pointer-events-none z-10"
          :style="{
            left: dragPreviewStyle.left + 'px',
            top: dragPreviewStyle.top + 'px',
            width: dragPreviewStyle.width + 'px',
            height: dragPreviewStyle.height + 'px'
          }"
        >
          <div class="flex items-center justify-center h-full text-primary-600 text-sm font-medium">
            Drop to create chart
          </div>
        </div>
        
        <!-- Tabs UI -->
        <nav v-if="showDashboardTabs" class="flex gap-2 px-1 mt-0 mb-2" aria-label="Dashboard Tabs" style="align-items: flex-start;">
          <div class="flex gap-2">
            <transition-group name="fade" tag="div" class="flex gap-2">
              <div
                v-for="tab in dashboardTabs"
                :key="tab.id"
                class="relative group flex items-center"
                @mouseenter="tabHoverId = tab.id"
                @mouseleave="tabHoverId = null"
              >
                <input
                  v-if="tab.id === editingTabId"
                  v-model="editingTabName"
                  :id="`tab-edit-input-${tab.id}`"
                  @blur="finishRenameTab(tab.id)"
                  @keyup.enter="finishRenameTab(tab.id)"
                  @keyup.esc="cancelRenameTab()"
                  @keydown="handleTabEditKey(tab.id, $event)"
                  class="px-2 py-1 border rounded text-sm w-28 mr-1 focus:ring-2 focus:ring-primary-500"
                  :style="'transition: box-shadow 0.2s;'"
                  autofocus
                />
                <button
                  v-else
                  @click="activeTabId = tab.id"
                  :class="[
                    'py-2.5 px-4 text-center font-medium text-sm transition-all duration-200 flex items-center gap-2 rounded-lg shadow-sm border relative',
                    activeTabId === tab.id
                      ? 'border-primary-200 text-primary-700 bg-primary-50 shadow-md z-10'
                      : 'border-gray-200 text-gray-600 bg-white hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300 hover:shadow-md'
                  ]"
                  :title="tab.name"
                >
                  <span>{{ tab.name }}</span>
                  <PencilIcon
                    v-if="tabHoverId === tab.id"
                    @click.stop="startRenameTab(tab.id)"
                    class="h-4 w-4 ml-1 text-gray-400 hover:text-primary-600 cursor-pointer transition-opacity duration-150 opacity-80 group-hover:opacity-100"
                  />
                  <button
                    v-if="dashboardTabs.length > 1 && tabHoverId === tab.id"
                    @click.stop="removeTab(tab.id)"
                    class="ml-1 text-gray-400 hover:text-red-500 bg-transparent rounded-full p-0.5 transition-opacity duration-150 opacity-80 group-hover:opacity-100"
                    style="z-index:20"
                  >
                    &times;
                  </button>
                </button>
              </div>
            </transition-group>
            <button @click="addTab" class="ml-2 px-2 py-1 bg-gray-100 text-gray-500 rounded hover:bg-primary-100 hover:text-primary-700 transition-colors duration-150 focus:outline-none border-none shadow-none">+</button>
          </div>
        </nav>
        <div class="bg-white rounded-lg shadow-sm h-full">
          <div class="p-6 h-full">
            <div v-if="charts.length === 0" class="flex items-center justify-center h-full text-gray-500">
              <div class="text-center">
                <Squares2X2Icon class="mx-auto h-12 w-12 mb-4" />
                <h3 class="text-lg font-medium text-gray-900 mb-2">Start Building Your Dashboard</h3>
                <p class="text-sm text-gray-500">
                  Select a data source, choose a chart type, and drag fields to create your first chart.
                </p>
                <p class="text-sm text-gray-400 mt-2">
                  Or drag a chart type from the left panel to create an empty chart.
                </p>
              </div>
            </div>
            <!-- GridStack Container -->
            <div v-else ref="gridStackRef" class="grid-stack h-full">
              <div
                v-for="chart in charts"
                :key="chart.id"
                class="grid-stack-item"
                :gs-id="chart.id"
                :gs-x="chart.layout.x"
                :gs-y="chart.layout.y"
                :gs-w="chart.layout.w"
                :gs-h="chart.layout.h"
              >
                <div class="grid-stack-item-content">
                  <div class="chart-header flex justify-end items-center gap-2">
                    <!-- 3-dot menu -->
                    <div class="relative chart-menu-container">
                      <button v-if="!previewMode" @click="toggleChartMenu(chart.id)" class="chart-menu-btn p-1 rounded-full hover:bg-gray-100 focus:outline-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><circle cx="5" cy="12" r="1.5"/><circle cx="12" cy="12" r="1.5"/><circle cx="19" cy="12" r="1.5"/></svg>
                      </button>
                      <div v-if="openChartMenuId === chart.id && !previewMode" class="absolute right-0 mt-2 w-40 bg-white border border-gray-200 rounded shadow-lg z-30">
                        <button @click="editChart(chart)" class="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100">Edit</button>
                        <button @click="exportChart(chart, 'pdf')" class="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100">Export PDF</button>
                        <button @click="exportChart(chart, 'png')" class="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100">Export to PNG</button>
                        <button @click="removeChart(chart.id)" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Remove</button>
                      </div>
                    </div>
                  </div>
                  <div 
                    class="chart-content"
                    @click="editChart(chart)"
                    :class="{ 'cursor-pointer': !previewMode }"
                  >
                    <ChartPreview :chart="chart" :key="`${chart.id}-${chart.updatedAt?.getTime() || chart.createdAt.getTime()}`" class="w-full h-full" />
                  </div>
                </div>
              </div>
            </div>
            <!-- Exit Preview Button -->
            <button v-if="previewMode" @click="previewMode = false" class="absolute top-4 right-4 z-50 px-4 py-2 bg-white text-primary-700 border border-gray-300 rounded shadow hover:bg-gray-50">Exit Preview</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Toast Notification -->
    <Toast
      :show="showToast"
      :type="toastType"
      :title="toastTitle"
      :message="toastMessage"
      @close="hideToast"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute } from 'vue-router'
import {
  ArrowLeftIcon,
  PlusIcon,
  XMarkIcon,
  Squares2X2Icon,
  DocumentCheckIcon,
  ChartBarIcon,
  PresentationChartLineIcon,
  ChartPieIcon,
  CircleStackIcon,
  Cog6ToothIcon,
  ChevronDownIcon,
  CheckIcon,
  PencilIcon,
  TrashIcon,
  ShareIcon
} from '@heroicons/vue/24/outline'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { useDataSourceStore } from '@/stores'
import type { DataSourceColumn } from '@/stores/modules/dataSource'
import type { DashboardChart } from '@/types/dashboard'
import ChartPreview from '@/components/charts/ChartPreview.vue'
import DataPanel from '@/components/data/DataPanel.vue'
import ChartPanel from '@/components/charts/ChartPanel.vue'
import Toast from '@/components/ui/Toast.vue'

// Import composables
import {
  useDashboardState,
  useTabManagement,
  useChartManagement,
  useDragAndDrop,
  useGridStack
} from './composables'

const route = useRoute()
const dataSourceStore = useDataSourceStore()

// Initialize composables
const {
  // State
  dashboardName,
  dashboardDescription,
  dashboardCategory,
  currentDashboardId,
  previewMode,
  selectedDataSources,
  expandedDataSources,
  showToast,
  toastType,
  toastTitle,
  toastMessage,
  
  // Computed
  hasUnsavedChanges,
  
  // Methods
  saveDashboard,
  loadDashboard,
  createTemporaryDashboard,
  toggleDataSource,
  updateSelectedDataSources,
  onUpdateDashboardInfo,
  showToastNotification,
  hideToast,
  goBack
} = useDashboardState()

const {
  // State
  dashboardTabs,
  activeTabId,
  editingTabId,
  editingTabName,
  tabHoverId,
  showDashboardTabs,
  
  // Computed
  charts,
  
  // Methods
  addTab,
  removeTab,
  startRenameTab,
  finishRenameTab,
  cancelRenameTab,
  handleTabEditKey,
  handleToggleDashboardTabs,
  loadTabs,
  addChartToActiveTab,
  removeChartFromAllTabs,
  injectDependencies: injectTabDependencies
} = useTabManagement()

const {
  // State
  chartConfig,
  selectedChartType,
  editingChartId,
  openChartMenuId,
  chartTypes,
  colorSchemes,
  colorPalettes,
  
  // Computed
  isChartConfigValid,
  
  // Methods
  resetChartConfig,
  addOrUpdateChart,
  addChart,
  editChart,
  removeChart,
  cancelEdit,
  exportChart,
  toggleChartMenu,
  createEmptyChart,
  injectDependencies: injectChartDependencies
} = useChartManagement()

const {
  // State
  isDragging,
  draggedChartType,
  dragStartX,
  dragStartY,
  currentDragX,
  currentDragY,
  
  // Methods
  handleDragStart,
  handleDragEnd,
  handleDragOver,
  handleDrop,
  handleDragEnter,
  handleDragLeave
} = useDragAndDrop()

const {
  // State
  gridStackRef,
  gridStackInstance,
  isGridInitialized,
  gridOptions,
  
  // Methods
  initializeGridStack,
  destroyGridStack,
  addChartToGrid,
  removeChartFromGrid,
  updateChartInGrid,
  refreshGrid,
  clearGrid,
  loadChartsToGrid,
  injectDependencies: injectGridDependencies
} = useGridStack()

// Additional refs for the component
const dataPanelRef = ref<InstanceType<typeof DataPanel>>()

// Resizable sidebar logic
const leftSidebarWidth = ref(240)
const chartTypeColWidth = ref(260)
const resizing = ref<'left' | 'chartType' | null>(null)
const startX = ref(0)
const startWidth = ref(0)

// Drag preview state
const showDragPreview = ref(false)
const dragPreviewStyle = ref({
  left: 0,
  top: 0,
  width: 0,
  height: 0
})

// Methods
const startResizing = (which: 'left' | 'chartType') => {
  resizing.value = which
  startX.value = window.event instanceof MouseEvent ? window.event.clientX : 0
  startWidth.value = which === 'left' ? leftSidebarWidth.value : chartTypeColWidth.value
  document.addEventListener('mousemove', onResizing)
  document.addEventListener('mouseup', stopResizing)
}

const onResizing = (e: MouseEvent) => {
  if (!resizing.value) return
  const dx = e.clientX - startX.value
  if (resizing.value === 'left') {
    let newWidth = startWidth.value + dx
    newWidth = Math.max(180, Math.min(400, newWidth))
    leftSidebarWidth.value = newWidth
  } else if (resizing.value === 'chartType') {
    let newWidth = startWidth.value + dx
    newWidth = Math.max(200, Math.min(400, newWidth))
    chartTypeColWidth.value = newWidth
  }
}

const stopResizing = () => {
  resizing.value = null
  document.removeEventListener('mousemove', onResizing)
  document.removeEventListener('mouseup', stopResizing)
}

// Add function to check if a field is in use
const isFieldInUse = (fieldName: string, dataSourceId: string) => {
  if (!selectedChartType.value || !chartConfig.dataSourceId) return false
  
  // Check if the field is from the same data source as the current chart
  if (chartConfig.dataSourceId !== dataSourceId) return false

  // Check if the field is used in any of the chart properties
  if (selectedChartType.value === 'pie') {
    return chartConfig.category === fieldName || chartConfig.value === fieldName
  } else if (selectedChartType.value === 'bar') {
    return (
      chartConfig.xAxis.includes(fieldName) ||
      chartConfig.yAxis === fieldName
    )
  } else if (selectedChartType.value === 'line' || selectedChartType.value === 'scatter') {
    return chartConfig.xAxis.includes(fieldName) || chartConfig.yAxis === fieldName
  } else if (selectedChartType.value === 'card') {
    return chartConfig.keyMetric === fieldName || chartConfig.previousMetric === fieldName
  }
  return false
}

// Data source manager methods
const openDataSourceManager = () => {
  if (dataPanelRef.value) {
    dataPanelRef.value.openDataSourceManager()
  }
}

// Field drag and drop
const onFieldDragStart = (event: DragEvent, column: DataSourceColumn, dataSourceId: string) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', JSON.stringify({
      name: column.name,
      type: column.type,
      dataSourceId
    }))
  }
}

const onFieldDrop = (event: DragEvent, target: 'xAxis' | 'yAxis' | 'category' | 'value' | 'keyMetric') => {
  event.preventDefault()
  if (!event.dataTransfer) return
  try {
    const fieldData = JSON.parse(event.dataTransfer.getData('text/plain'))
    
    // Validate field type for Y-axis (should be numeric)
    if (target === 'yAxis' && fieldData.type !== 'number') {
      alert('Y-axis requires a numeric field')
      return
    }

    // Check if we already have fields from a different data source
    if (chartConfig.dataSourceId && chartConfig.dataSourceId !== fieldData.dataSourceId) {
      if (selectedChartType.value === 'bar' && chartConfig.xAxis.length > 0) {
        alert('Cannot mix fields from different data sources in the same chart')
        return
      }
    }

    if (target === 'xAxis' && selectedChartType.value === 'bar') {
      // Add to array, no duplicates
      if (!chartConfig.xAxis.includes(fieldData.name)) {
        chartConfig.xAxis.push(fieldData.name)
      }
    } else if (target === 'xAxis') {
      chartConfig.xAxis = [fieldData.name]
    } else if (target === 'yAxis') {
      chartConfig.yAxis = fieldData.name
    } else if (target === 'category') {
      chartConfig.category = fieldData.name
    } else if (target === 'value') {
      chartConfig.value = fieldData.name
    } else if (target === 'keyMetric') {
      chartConfig.keyMetric = fieldData.name
    }
    
    // Store the data source ID for the chart
    chartConfig.dataSourceId = fieldData.dataSourceId
  } catch (error) {
    console.error('Failed to parse dropped field data:', error)
  }
}

// Click outside handler for chart menu
const handleClickOutside = (event: Event) => {
  if (openChartMenuId.value) {
    const target = event.target as HTMLElement
    // Check if the click target is within any chart menu container
    const isWithinChartMenu = target.closest('.chart-menu-container')
    if (!isWithinChartMenu) {
      openChartMenuId.value = null
    }
  }
}

// Drag and drop handlers for chart types
const onDragEnter = (event: DragEvent) => {
  event.preventDefault()
  if (!event.dataTransfer) return
  
  try {
    const data = JSON.parse(event.dataTransfer.getData('application/json'))
    if (data.chartType) {
      showDragPreview.value = true
      updateDragPreviewPosition(event)
    }
  } catch (error) {
    // If we can't parse the data, it might not be a chart type drag
    // We'll still show the preview for any drag operation
    showDragPreview.value = true
    updateDragPreviewPosition(event)
  }
}

const onDragLeave = (event: DragEvent) => {
  // Only hide preview if we're leaving the dashboard area completely
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const x = event.clientX
  const y = event.clientY
  
  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    showDragPreview.value = false
  }
}

const updateDragPreviewPosition = (event: DragEvent) => {
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  // Calculate preview dimensions
  const cellWidth = (window.innerWidth - 240 - 260 - 48) / 12
  const cellHeight = 70 + 10
  const previewWidth = 4 * cellWidth - 24
  const previewHeight = 3 * cellHeight - 24
  
  // Calculate preview position (smooth, centered on mouse)
  let previewLeft = x - (previewWidth / 2)
  let previewTop = y - (previewHeight / 2)
  
  // Check if the preview would go outside the dashboard area
  const dashboardWidth = rect.width - 24
  const dashboardHeight = rect.height - 24
  
  // Constrain preview within dashboard boundaries
  previewLeft = Math.max(12, Math.min(previewLeft, dashboardWidth - previewWidth - 12))
  previewTop = Math.max(12, Math.min(previewTop, dashboardHeight - previewHeight - 12))
  
  // Check if preview is within boundaries
  const isWithinBoundaries = 
    previewLeft >= 12 && 
    previewTop >= 12 && 
    previewLeft + previewWidth <= dashboardWidth - 12 && 
    previewTop + previewHeight <= dashboardHeight - 12
  
  // Hide preview if outside boundaries
  if (!isWithinBoundaries) {
    showDragPreview.value = false
    return
  }
  
  // Update preview position
  dragPreviewStyle.value = {
    left: previewLeft,
    top: previewTop,
    width: previewWidth,
    height: previewHeight
  }
  
  // Show preview if it was hidden and now is within boundaries
  if (!showDragPreview.value) {
    showDragPreview.value = true
  }
}

const onChartTypeDrop = (event: DragEvent) => {
  event.preventDefault()
  showDragPreview.value = false // Hide preview when dropping
  
  if (!event.dataTransfer) return
  
  try {
    const data = JSON.parse(event.dataTransfer.getData('application/json'))
    if (data.chartType) {
      // Get mouse position relative to the dashboard area
      const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      createEmptyChart(data.chartType, x, y)
    }
  } catch (error) {
    console.error('Failed to parse dropped chart type data:', error)
  }
}

const onDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (showDragPreview.value) {
    updateDragPreviewPosition(event)
  }
}

// Load dashboard on mount
onMounted(async () => {
  // Inject dependencies into composables
  injectTabDependencies(currentDashboardId, initializeGridStack)
  injectChartDependencies(currentDashboardId, initializeGridStack)
  injectGridDependencies(currentDashboardId)
  
  const dashboardId = route.query.id as string | undefined
  if (dashboardId) {
    // Load dashboard for editing
    await loadDashboard(dashboardId)
    
    // Load tabs
    const dashboard = dataSourceStore.getDashboardById?.(dashboardId)
    if (dashboard?.tabs && dashboard.tabs.length > 0) {
      loadTabs(dashboard.tabs)
    }
    
    await nextTick()
    initializeGridStack()
  }
  
  // Add click outside listener for chart menu
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  destroyGridStack()
  document.removeEventListener('mousemove', onResizing)
  document.removeEventListener('mouseup', stopResizing)
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.chart-header {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.chart-menu-btn {
  color: #6b7280;
}
.chart-menu-btn:hover {
  color: #374151;
}

.chart-remove-btn {
  @apply bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 transition-opacity duration-200;
}

.chart-content {
  height: 100%;
  padding: 8px;
}

.grid-stack-item-content {
  position: relative;
  height: 100%;
  cursor: move;
}

/* GridStack overrides */
:deep(.grid-stack-item.ui-draggable-dragging) {
  opacity: 0.8;
}

:deep(.grid-stack-item.ui-resizable-resizing) {
  opacity: 0.8;
}

/* Drag and drop styling */
.border-dashed:hover {
  @apply border-primary-400;
}

.resizer {
  transition: background 0.2s;
}
.resizer:hover {
  background: #d1d5db;
}
</style>