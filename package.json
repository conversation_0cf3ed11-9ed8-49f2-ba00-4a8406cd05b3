{"name": "vue-bi-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@vitest/ui": "^3.2.4", "@vue/test-utils": "^2.4.6", "axios": "^1.10.0", "chart.js": "^4.4.0", "gridstack": "^10.1.2", "jsdom": "^26.1.0", "papaparse": "^5.4.1", "pinia": "^2.1.7", "sweetalert2": "^11.22.1", "vitest": "^3.2.4", "vue": "^3.4.38", "vue-chartjs": "^5.2.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/papaparse": "^5.3.14", "@vitejs/plugin-vue": "^5.1.3", "autoprefixer": "^10.4.16", "gh-pages": "^6.3.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4"}}